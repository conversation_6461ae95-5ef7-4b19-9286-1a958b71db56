'use client'

import { useEffect, useState } from 'react'
import { useTranslations } from 'next-intl'
import { useUser } from '@/providers/UserProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Trash2, User, MapPin, Globe, Link, GitBranch, X, Share2, Camera, Play, Building2 } from 'lucide-react'
import { api } from '@/services/api'
import { ProfileData, SocialLink } from '@/services/types'
import toast from '@/utils/toast'

// 社交平台配置
const SOCIAL_PLATFORMS = [
  { id: 'twitter', name: 'X (Twitter)', icon: X, color: '#000000' },
  { id: 'facebook', name: 'Facebook', icon: Share2, color: '#1877F2' },
  { id: 'github', name: 'GitH<PERSON>', icon: GitBranch, color: '#181717' },
  { id: 'linkedin', name: 'LinkedIn', icon: Building2, color: '#0A66C2' },
  { id: 'instagram', name: 'Instagram', icon: Camera, color: '#E4405F' },
  { id: 'youtube', name: 'YouTube', icon: Play, color: '#FF0000' },
  { id: 'bilibili', name: '哔哩哔哩', icon: null, color: '#00A1D6' },
  { id: 'xiaohongshu', name: '小红书', icon: null, color: '#FF2442' },
  { id: 'weibo', name: '微博', icon: null, color: '#E6162D' },
  { id: 'tiktok', name: 'TikTok', icon: null, color: '#000000' },
  { id: 'custom', name: '自定义', icon: Link, color: '#6B7280' },
]

// 平台图标组件
const PlatformIcon = ({ platform, className = "w-4 h-4" }: { platform: string; className?: string }) => {
  const platformConfig = SOCIAL_PLATFORMS.find(p => p.id === platform)

  if (!platformConfig) {
    return <Link className={className} />
  }

  if (platformConfig.icon) {
    const IconComponent = platformConfig.icon
    return <IconComponent className={className} style={{ color: platformConfig.color }} />
  }

  // 对于没有 Lucide 图标的平台，使用文字缩写
  const getAbbreviation = (id: string) => {
    switch (id) {
      case 'bilibili': return 'B'
      case 'xiaohongshu': return '小'
      case 'weibo': return '微'
      case 'tiktok': return 'T'
      default: return '?'
    }
  }

  return (
    <div
      className={`${className} rounded-full flex items-center justify-center text-white font-bold text-xs`}
      style={{ backgroundColor: platformConfig.color }}
    >
      {getAbbreviation(platform)}
    </div>
  )
}

export default function ProfilePage() {
  const t = useTranslations('profile')
  const { userData, refreshUser } = useUser()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [profileData, setProfileData] = useState<ProfileData>({
    bio: '',
    location: '',
    website: '',
    socialLinks: [],
  })

  // 加载用户配置数据
  useEffect(() => {
    const loadProfile = async () => {
      if (!userData) return

      try {
        const response = await api.user.getProfile()
        if (response.success && response.data) {
          setProfileData(response.data)
        }
      } catch (error) {
        console.error('Failed to load profile:', error)
      } finally {
        setLoading(false)
      }
    }

    loadProfile()
  }, [userData])

  // 处理基本信息输入变化
  const handleBasicInfoChange = (
    field: keyof Pick<ProfileData, 'bio' | 'location' | 'website'>,
    value: string
  ) => {
    setProfileData((prev) => ({
      ...prev,
      [field]: value,
    }))
  }

  // 添加社交链接
  const addSocialLink = (platformId?: string) => {
    const platform = platformId || 'custom'
    const platformConfig = SOCIAL_PLATFORMS.find(p => p.id === platform)
    const label = platformConfig && platform !== 'custom' ? platformConfig.name : ''

    setProfileData((prev) => ({
      ...prev,
      socialLinks: [
        ...(prev.socialLinks || []),
        { platform, url: '', label },
      ],
    }))
  }

  // 更新社交链接
  const updateSocialLink = (
    index: number,
    field: keyof SocialLink,
    value: string
  ) => {
    setProfileData((prev) => ({
      ...prev,
      socialLinks:
        prev.socialLinks?.map((link, i) =>
          i === index ? { ...link, [field]: value } : link
        ) || [],
    }))
  }

  // 删除社交链接
  const removeSocialLink = (index: number) => {
    setProfileData((prev) => ({
      ...prev,
      socialLinks: prev.socialLinks?.filter((_, i) => i !== index) || [],
    }))
  }

  // 保存配置
  const handleSave = async () => {
    if (!userData) return

    setSaving(true)
    try {
      const response = await api.user.updateProfile(profileData)
      if (response.success) {
        toast.success(t('saved'))
        // 刷新用户数据
        await refreshUser()
      } else {
        toast.error(t('error'))
      }
    } catch (error) {
      console.error('Failed to save profile:', error)
      toast.error(t('error'))
    } finally {
      setSaving(false)
    }
  }

  // 如果用户未登录，显示提示
  if (!userData) {
    return (
      <div className="min-h-screen pt-28 px-8">
        <div className="max-w-2xl mx-auto text-center">
          <h1 className="text-2xl font-bold mb-4">请先登录</h1>
          <p className="text-gray-600">您需要登录后才能访问个人设置页面。</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen pt-28 px-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">{t('title')}</h1>

        {/* 用户基本信息 */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="w-12 h-12 rounded-full overflow-hidden ring-2 ring-slate-200">
                <img
                  src={userData.image}
                  alt="Avatar"
                  className="w-full h-full object-cover"
                />
              </div>
              <div>
                <h2 className="text-xl font-semibold">{userData.name}</h2>
                <p className="text-sm text-gray-600">{userData.email}</p>
              </div>
            </CardTitle>
          </CardHeader>
        </Card>

        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">加载中...</p>
          </div>
        ) : (
          <div className="grid gap-8 md:grid-cols-2">
            {/* 基本信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  {t('basicInfo')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 个人简介 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">
                    {t('bio')}
                  </label>
                  <Textarea
                    placeholder={t('placeholder.bio')}
                    value={profileData.bio || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('bio', e.target.value)
                    }
                    className="min-h-[100px] resize-none"
                  />
                </div>

                {/* 所在地 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    {t('location')}
                  </label>
                  <Input
                    type="text"
                    placeholder={t('placeholder.location')}
                    value={profileData.location || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('location', e.target.value)
                    }
                  />
                </div>

                {/* 个人网站 */}
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700 flex items-center gap-2">
                    <Globe className="w-4 h-4" />
                    {t('website')}
                  </label>
                  <Input
                    type="url"
                    placeholder={t('placeholder.website')}
                    value={profileData.website || ''}
                    onChange={(e) =>
                      handleBasicInfoChange('website', e.target.value)
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* 社交链接 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Link className="w-5 h-5" />
                  {t('socialLinks')}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* 预设平台快速添加 */}
                <div className="space-y-3">
                  <label className="text-sm font-medium text-gray-700">
                    {t('popularPlatforms')}
                  </label>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                    {SOCIAL_PLATFORMS.filter(p => p.id !== 'custom').map((platform) => (
                      <Button
                        key={platform.id}
                        variant="outline"
                        size="sm"
                        onClick={() => addSocialLink(platform.id)}
                        className="flex items-center gap-2 justify-start h-9"
                        disabled={profileData.socialLinks?.some(link => link.platform === platform.id)}
                      >
                        <PlatformIcon platform={platform.id} className="w-4 h-4" />
                        <span className="text-xs truncate">{platform.name}</span>
                      </Button>
                    ))}
                  </div>
                </div>

                {/* 自定义链接添加 */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <span className="text-sm font-medium text-gray-700">
                    {t('customLinks')}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addSocialLink('custom')}
                    className="flex items-center gap-2"
                  >
                    <Plus className="w-4 h-4" />
                    {t('addCustomLink')}
                  </Button>
                </div>

                {/* 已添加的社交链接 */}
                {profileData.socialLinks && profileData.socialLinks.length > 0 ? (
                  <div className="space-y-3">
                    {profileData.socialLinks.map((link, index) => (
                      <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                        <PlatformIcon platform={link.platform} className="w-5 h-5 flex-shrink-0" />

                        <div className="flex-1 space-y-2">
                          {/* 平台选择器 */}
                          <Select
                            value={link.platform}
                            onValueChange={(value) => updateSocialLink(index, 'platform', value)}
                          >
                            <SelectTrigger className="h-8 text-sm">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {SOCIAL_PLATFORMS.map((platform) => (
                                <SelectItem key={platform.id} value={platform.id}>
                                  <div className="flex items-center gap-2">
                                    <PlatformIcon platform={platform.id} className="w-4 h-4" />
                                    {platform.name}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            {/* 标签 */}
                            <Input
                              type="text"
                              placeholder={t('placeholder.label')}
                              value={link.label || ''}
                              onChange={(e) =>
                                updateSocialLink(index, 'label', e.target.value)
                              }
                              className="text-sm h-8"
                            />

                            {/* URL */}
                            <Input
                              type="url"
                              placeholder={t('placeholder.url')}
                              value={link.url}
                              onChange={(e) =>
                                updateSocialLink(index, 'url', e.target.value)
                              }
                              className="text-sm h-8"
                            />
                          </div>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeSocialLink(index)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 text-gray-500">
                    <Link className="w-6 h-6 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">{t('noLinksYet')}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {/* 保存按钮 */}
        {!loading && (
          <div className="mt-8">
            <Button
              onClick={handleSave}
              disabled={saving}
              className="w-full bg-slate-900 hover:bg-slate-800 text-white"
              size="lg"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  保存中...
                </>
              ) : (
                t('save')
              )}
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
